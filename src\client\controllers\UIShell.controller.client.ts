import { Controller, OnStart } from "@flamework/core";
import React from "@rbxts/react";
import { createRoot } from "@rbxts/react-roblox";
import { Players } from "@rbxts/services";
import { App } from "client/ui/App";

@Controller()
export class UIShellController implements OnStart {
	private root?: ReturnType<typeof createRoot>;
	private screenGui?: ScreenGui;
	private characterConnection?: RBXScriptConnection;

	onStart(): void {
		const playerGui = Players.LocalPlayer.WaitForChild("PlayerGui") as PlayerGui;
		
		// Create the main UI container
		this.screenGui = new Instance("ScreenGui");
		this.screenGui.Name = "AppUI";
		this.screenGui.ResetOnSpawn = false;
		this.screenGui.IgnoreGuiInset = true;
		this.screenGui.Parent = playerGui;

		// Create React root and render the app
		this.root = createRoot(this.screenGui);
		this.root.render(React.createElement(App));

		// Handle character removal for cleanup
		this.characterConnection = Players.LocalPlayer.CharacterRemoving.Connect(() => {
			this.unmount();
		});
	}

	private unmount(): void {
		if (this.root) {
			this.root.unmount();
			this.root = undefined;
		}

		if (this.screenGui) {
			this.screenGui.Destroy();
			this.screenGui = undefined;
		}

		if (this.characterConnection) {
			this.characterConnection.Disconnect();
			this.characterConnection = undefined;
		}
	}

	destroy(): void {
		this.unmount();
	}
}