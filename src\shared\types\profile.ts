// File: src/shared/types/player.ts
export interface PlayerProfile {
  cash: number;
  xp: number;
  level: number;
  wanted: number;
  ownedVehicles: string[];
  ownedWeapons: string[];
  settings: {
    fov: number;
    sensitivity: number;
  };
}

export interface PlayerStats {
  kills: number;
  deaths: number;
  arrests: number;
  robberies: number;
  playtime: number;
}

export interface PlayerInventory {
  weapons: Map<string, WeaponData>;
  items: Map<string, number>;
  keyCards: string[];
}

export interface WeaponData {
  id: string;
  ammo: number;
  attachments: string[];
}

// File: src/shared/types/entities.ts
export interface VehicleSpawnData {
  id: string;
  modelName: string;
}

export interface VehicleConfig {
  id: string;
  name: string;
  modelName: string;
  price: number;
  maxSpeed: number;
  acceleration: number;
  handling: number;
  team?: TeamId;
  spawnLocations: Vector3[];
}

export interface WeaponConfig {
  id: string;
  name: string;
  damage: number;
  range: number;
  fireRate: number;
  ammoCapacity: number;
  price: number;
  team?: TeamId;
}

export interface BuildingData {
  id: string;
  name: string;
  position: Vector3;
  size: Vector3;
  type: BuildingType;
  accessible: boolean;
  team?: TeamId;
}

export type BuildingType = "Bank" | "Store" | "Police" | "Hospital" | "Garage" | "House";

// File: src/shared/types/teams.ts
export type TeamId = "Police" | "Criminals" | "Heroes" | "Civilians";

export interface TeamData {
  id: TeamId;
  name: string;
  color: Color3;
  spawnPoints: Vector3[];
  allowedVehicles: string[];
  allowedWeapons: string[];
}

export interface TeamMember {
  userId: number;
  username: string;
  rank: number;
  joinTime: number;
}

// File: src/shared/types/game.ts
export interface GameState {
  activeHeists: Map<string, HeistData>;
  serverTime: number;
  weather: WeatherType;
  timeOfDay: number;
}

export interface HeistData {
  id: string;
  location: string;
  participants: number[];
  startTime: number;
  reward: number;
  difficulty: HeistDifficulty;
  status: HeistStatus;
}

export type WeatherType = "Clear" | "Cloudy" | "Rain" | "Storm" | "Fog";
export type HeistDifficulty = "Easy" | "Medium" | "Hard" | "Expert";
export type HeistStatus = "Preparing" | "Active" | "Completed" | "Failed";

// File: src/shared/types/events.ts
export interface PlayerJoinedData {
  player: Player;
  profile: PlayerProfile;
  team: TeamId;
}

export interface PlayerLeftData {
  userId: number;
  username: string;
}

export interface TeamChangeData {
  userId: number;
  oldTeam: TeamId;
  newTeam: TeamId;
}

export interface VehicleSpawnedData {
  vehicleId: string;
  ownerId: number;
  position: Vector3;
}

export interface WeaponPurchasedData {
  userId: number;
  weaponId: string;
  price: number;
}

export interface HeistStartedData {
  heistId: string;
  location: string;
  participants: number[];
}

// File: src/shared/types/common.ts
export interface BaseEntity {
  id: string;
  name: string;
  createdAt: number;
  updatedAt: number;
}

export interface Position3D {
  x: number;
  y: number;
  z: number;
}

export interface Rotation3D {
  x: number;
  y: number;
  z: number;
}

export interface Transform {
  position: Position3D;
  rotation: Rotation3D;
  scale: Position3D;
}

export interface NetworkPacket<T = unknown> {
  type: string;
  data: T;
  timestamp: number;
  senderId?: number;
}

export interface ValidationResult {
  success: boolean;
  errors: string[];
}

export interface ServerResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredKeys<T> = {
  [K in keyof T]-?: {} extends Pick<T, K> ? never : K;
}[keyof T];
export type OptionalKeys<T> = {
  [K in keyof T]-?: {} extends Pick<T, K> ? K : never;
}[keyof T];