import React from "@rbxts/react";
import { usePx } from "client/ui/hooks/use-px";

export function App(): JSX.Element {
  const px = usePx();

  return (
    <frame
      Size={UDim2.fromScale(1, 1)}
      Position={UDim2.fromScale(0, 0)}
      BackgroundTransparency={1}
      BorderSizePixel={0}
    >
      {/* Top-left HUD Panel - Cash/XP/Level */}
      <frame
        Size={UDim2.fromOffset(px(300), px(120))}
        Position={UDim2.fromOffset(px(20), px(20))}
        BackgroundColor3={Color3.fromRGB(0, 0, 0)}
        BackgroundTransparency={0.3}
        BorderSizePixel={0}
      >
        <uicorner CornerRadius={new UDim(0, px(8))} />
        <uistroke
          Color={Color3.fromRGB(255, 255, 255)}
          Transparency={0.8}
          Thickness={px(1)}
        />
        
        <textlabel
          Size={UDim2.fromScale(1, 0.33)}
          Position={UDim2.fromScale(0, 0)}
          BackgroundTransparency={1}
          Text="Cash: $50,000"
          TextColor3={Color3.fromRGB(255, 255, 255)}
          TextSize={px(16)}
          TextXAlignment={Enum.TextXAlignment.Left}
          TextYAlignment={Enum.TextYAlignment.Center}
        >
          <uipadding
            PaddingLeft={new UDim(0, px(12))}
            PaddingRight={new UDim(0, px(12))}
          />
          <uitextsizeconstraint MaxTextSize={px(20)} MinTextSize={px(12)} />
        </textlabel>
        
        <textlabel
          Size={UDim2.fromScale(1, 0.33)}
          Position={UDim2.fromScale(0, 0.33)}
          BackgroundTransparency={1}
          Text="XP: 1,250 / 2,000"
          TextColor3={Color3.fromRGB(100, 200, 255)}
          TextSize={px(16)}
          TextXAlignment={Enum.TextXAlignment.Left}
          TextYAlignment={Enum.TextYAlignment.Center}
        >
          <uipadding
            PaddingLeft={new UDim(0, px(12))}
            PaddingRight={new UDim(0, px(12))}
          />
          <uitextsizeconstraint MaxTextSize={px(20)} MinTextSize={px(12)} />
        </textlabel>
        
        <textlabel
          Size={UDim2.fromScale(1, 0.34)}
          Position={UDim2.fromScale(0, 0.66)}
          BackgroundTransparency={1}
          Text="Level: 15"
          TextColor3={Color3.fromRGB(255, 215, 0)}
          TextSize={px(16)}
          TextXAlignment={Enum.TextXAlignment.Left}
          TextYAlignment={Enum.TextYAlignment.Center}
        >
          <uipadding
            PaddingLeft={new UDim(0, px(12))}
            PaddingRight={new UDim(0, px(12))}
          />
          <uitextsizeconstraint MaxTextSize={px(20)} MinTextSize={px(12)} />
        </textlabel>
      </frame>

      {/* Center Crosshair */}
      <frame
        Size={UDim2.fromOffset(px(40), px(40))}
        Position={UDim2.fromScale(0.5, 0.5)}
        AnchorPoint={new Vector2(0.5, 0.5)}
        BackgroundTransparency={1}
        BorderSizePixel={0}
      >
        {/* Horizontal crosshair line */}
        <frame
          Size={UDim2.fromOffset(px(20), px(2))}
          Position={UDim2.fromScale(0.5, 0.5)}
          AnchorPoint={new Vector2(0.5, 0.5)}
          BackgroundColor3={Color3.fromRGB(255, 255, 255)}
          BackgroundTransparency={0.2}
          BorderSizePixel={0}
        />
        
        {/* Vertical crosshair line */}
        <frame
          Size={UDim2.fromOffset(px(2), px(20))}
          Position={UDim2.fromScale(0.5, 0.5)}
          AnchorPoint={new Vector2(0.5, 0.5)}
          BackgroundColor3={Color3.fromRGB(255, 255, 255)}
          BackgroundTransparency={0.2}
          BorderSizePixel={0}
        />
      </frame>

      {/* Bottom-left MiniMap */}
      <frame
        Size={UDim2.fromOffset(px(200), px(200))}
        Position={UDim2.fromOffset(px(20), px(-220))}
        AnchorPoint={new Vector2(0, 1)}
        BackgroundColor3={Color3.fromRGB(20, 20, 20)}
        BackgroundTransparency={0.2}
        BorderSizePixel={0}
      >
        <uicorner CornerRadius={new UDim(0, px(8))} />
        <uistroke
          Color={Color3.fromRGB(255, 255, 255)}
          Transparency={0.7}
          Thickness={px(2)}
        />
        
        <textlabel
          Size={UDim2.fromScale(1, 1)}
          Position={UDim2.fromScale(0, 0)}
          BackgroundTransparency={1}
          Text="MINIMAP"
          TextColor3={Color3.fromRGB(200, 200, 200)}
          TextSize={px(18)}
          TextXAlignment={Enum.TextXAlignment.Center}
          TextYAlignment={Enum.TextYAlignment.Center}
        >
          <uitextsizeconstraint MaxTextSize={px(24)} MinTextSize={px(14)} />
        </textlabel>
      </frame>

      {/* Bottom-right Notifications */}
      <frame
        Size={UDim2.fromOffset(px(300), px(150))}
        Position={UDim2.fromOffset(px(-20), px(-20))}
        AnchorPoint={new Vector2(1, 1)}
        BackgroundTransparency={1}
        BorderSizePixel={0}
      >
        <uilistlayout
          FillDirection={Enum.FillDirection.Vertical}
          HorizontalAlignment={Enum.HorizontalAlignment.Right}
          VerticalAlignment={Enum.VerticalAlignment.Bottom}
          Padding={new UDim(0, px(8))}
        />
        
        {/* Sample notification */}
        <frame
          Size={UDim2.fromOffset(px(280), px(60))}
          BackgroundColor3={Color3.fromRGB(50, 50, 50)}
          BackgroundTransparency={0.2}
          BorderSizePixel={0}
        >
          <uicorner CornerRadius={new UDim(0, px(6))} />
          <uistroke
            Color={Color3.fromRGB(255, 255, 255)}
            Transparency={0.8}
            Thickness={px(1)}
          />
          
          <textlabel
            Size={UDim2.fromScale(1, 1)}
            Position={UDim2.fromScale(0, 0)}
            BackgroundTransparency={1}
            Text="Achievement Unlocked!"
            TextColor3={Color3.fromRGB(255, 255, 255)}
            TextSize={px(14)}
            TextXAlignment={Enum.TextXAlignment.Center}
            TextYAlignment={Enum.TextYAlignment.Center}
          >
            <uipadding
              PaddingLeft={new UDim(0, px(8))}
              PaddingRight={new UDim(0, px(8))}
            />
            <uitextsizeconstraint MaxTextSize={px(18)} MinTextSize={px(12)} />
          </textlabel>
        </frame>
      </frame>

      {/* Bottom-center Speedometer/Nitro */}
      <frame
        Size={UDim2.fromOffset(px(250), px(80))}
        Position={UDim2.fromOffset(px(0), px(-20))}
        AnchorPoint={new Vector2(0.5, 1)}
        BackgroundColor3={Color3.fromRGB(0, 0, 0)}
        BackgroundTransparency={0.3}
        BorderSizePixel={0}
      >
        <uicorner CornerRadius={new UDim(0, px(8))} />
        <uistroke
          Color={Color3.fromRGB(255, 255, 255)}
          Transparency={0.8}
          Thickness={px(1)}
        />
        
        <frame
          Size={UDim2.fromScale(0.5, 1)}
          Position={UDim2.fromScale(0, 0)}
          BackgroundTransparency={1}
          BorderSizePixel={0}
        >
          <textlabel
            Size={UDim2.fromScale(1, 0.5)}
            Position={UDim2.fromScale(0, 0)}
            BackgroundTransparency={1}
            Text="SPEED"
            TextColor3={Color3.fromRGB(200, 200, 200)}
            TextSize={px(12)}
            TextXAlignment={Enum.TextXAlignment.Center}
            TextYAlignment={Enum.TextYAlignment.Bottom}
          >
            <uitextsizeconstraint MaxTextSize={px(16)} MinTextSize={px(10)} />
          </textlabel>
          
          <textlabel
            Size={UDim2.fromScale(1, 0.5)}
            Position={UDim2.fromScale(0, 0.5)}
            BackgroundTransparency={1}
            Text="95 MPH"
            TextColor3={Color3.fromRGB(255, 255, 255)}
            TextSize={px(16)}
            TextXAlignment={Enum.TextXAlignment.Center}
            TextYAlignment={Enum.TextYAlignment.Top}
          >
            <uitextsizeconstraint MaxTextSize={px(20)} MinTextSize={px(12)} />
          </textlabel>
        </frame>
        
        <frame
          Size={UDim2.fromScale(0.5, 1)}
          Position={UDim2.fromScale(0.5, 0)}
          BackgroundTransparency={1}
          BorderSizePixel={0}
        >
          <textlabel
            Size={UDim2.fromScale(1, 0.5)}
            Position={UDim2.fromScale(0, 0)}
            BackgroundTransparency={1}
            Text="NITRO"
            TextColor3={Color3.fromRGB(200, 200, 200)}
            TextSize={px(12)}
            TextXAlignment={Enum.TextXAlignment.Center}
            TextYAlignment={Enum.TextYAlignment.Bottom}
          >
            <uitextsizeconstraint MaxTextSize={px(16)} MinTextSize={px(10)} />
          </textlabel>
          
          <frame
            Size={UDim2.fromScale(0.8, 0.2)}
            Position={UDim2.fromScale(0.1, 0.65)}
            BackgroundColor3={Color3.fromRGB(60, 60, 60)}
            BorderSizePixel={0}
          >
            <uicorner CornerRadius={new UDim(0, px(4))} />
            
            <frame
              Size={UDim2.fromScale(0.75, 1)}
              Position={UDim2.fromScale(0, 0)}
              BackgroundColor3={Color3.fromRGB(0, 150, 255)}
              BorderSizePixel={0}
            >
              <uicorner CornerRadius={new UDim(0, px(4))} />
            </frame>
          </frame>
        </frame>
      </frame>
    </frame>
  );
}