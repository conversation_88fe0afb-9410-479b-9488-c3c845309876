{"version": 1, "flameworkVersion": "1.3.1", "identifiers": {"bloxstack-project:out/client/controllers/UIShell.controller.client@UIShellController": "client/controllers/UIShell.controller.client@UIShellController", "bloxstack-project:out/client/controllers/UIShellController.client@UIShellController": "client/controllers/UIShellController.client@UIShellController", "bloxstack-project:out/server/services/DataService.server@DataService": "server/services/DataService.server@DataService", "bloxstack-project:out/server/services/TeamService.server@TeamService": "server/services/TeamService.server@TeamService", "bloxstack-project:out/shared/networking/init@GlobalEvents": "shared/networking/init@GlobalEvents", "bloxstack-project:out/shared/networking/init@GlobalFunctions": "shared/networking/init@GlobalFunctions"}, "metadata": {}}