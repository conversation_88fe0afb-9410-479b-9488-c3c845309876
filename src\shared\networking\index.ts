import { Networking } from "@flamework/networking";

// Player profile interface
export interface PlayerProfile {
	cash: number;
	xp: number;
	level: number;
	wanted: number;
	ownedVehicles: string[];
	settings: Record<string, unknown>;
}

// Team types
export type TeamType = "Civilians" | "Police" | "Criminals" | "Heroes";

// Item types for purchases
export type ItemType = "vehicle" | "cosmetic" | "boost";

// Purchase result interface
export interface PurchaseResult {
	ok: boolean;
	reason?: string;
}

// Client to Server Events (object-map style)
export interface ClientToServerEvents {
	requestTeamChange: (team: TeamType) => void;
	requestSpawnVehicle: (vehicleId: string) => void;
	updatePlayerPosition: (position: Vector3) => void;
	playerReady: () => void;
	chatMessage: (message: string) => void;
}

// Server to Client Events (object-map style)
export interface ServerToClientEvents {
	profileUpdated: (profile: PlayerProfile) => void;
	notification: (message: string) => void;
	vehicleSpawned: (vehicleId: string, position: Vector3) => void;
	teamChanged: (team: TeamType) => void;
	playerJoined: (playerId: number, name: string) => void;
	playerLeft: (playerId: number) => void;
}

// Client to Server Functions (object-map style)
export interface ClientToServerFunctions {
	requestTeamChange: (team: TeamType) => boolean;
	purchaseItem: (itemType: ItemType, itemId: string) => PurchaseResult;
	getProfile: () => PlayerProfile;
	validateAction: (action: string) => boolean;
}

// Server to Client Functions (object-map style)
export interface ServerToClientFunctions {
	confirmPurchase: (itemId: string) => boolean;
	getPlayerData: (playerId: number) => PlayerProfile | undefined;
}

// Create global networking instances
export const GlobalEvents = Networking.createEvent<ServerToClientEvents, ClientToServerEvents>();

export const GlobalFunctions = Networking.createFunction<ServerToClientFunctions, ClientToServerFunctions>();