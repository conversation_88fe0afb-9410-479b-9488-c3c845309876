{"name": "bloxstack-project", "version": "0.2.2-alpha", "main": "index.js", "bloxstack": {"features": ["dev-server"]}, "scripts": {"build": "rbxtsc", "dev": "rbxtsc -w"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@rbxts/compiler-types": "^3.0.0-types.0", "@rbxts/types": "^1.0.857", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-roblox-ts": "^0.0.36", "prettier": "^3.5.3", "roblox-ts": "^3.0.0", "typescript": "5.5.3"}, "dependencies": {"@flamework/components": "^1.3.1", "@flamework/core": "^1.3.1", "@flamework/networking": "^1.3.1", "@rbxts/services": "latest", "@rbxts/ripple": "^0.9.3", "@rbxts/charm": "^0.10.0", "@rbxts/janitor": "^1.18.3-ts.0", "@rbxts/jsnatives": "^1.1.5", "@rbxts/pretty-react-hooks": "^0.6.4", "@rbxts/react": "^17.2.3", "@rbxts/react-charm": "^0.3.0", "@rbxts/react-roblox": "^17.2.3", "rbxts-transformer-flamework": "^1.3.1", "@rbxts/flamework-react-utils": "^1.0.6"}}