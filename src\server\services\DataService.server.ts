import { Service, OnStart } from "@flamework/core";
import { Players, DataStoreService } from "@rbxts/services";
import { PlayerProfile } from "shared/networking";

@Service()
export class DataService implements OnStart {
	private readonly dataStore = DataStoreService.GetDataStore("Profiles");
	private readonly playerProfiles = new Map<Player, PlayerProfile>();
	private readonly connections: RBXScriptConnection[] = [];

	private readonly defaultProfile: PlayerProfile = {
		cash: 0,
		xp: 0,
		level: 1,
		wanted: 0,
		ownedVehicles: [],
		settings: {}
	};

	onStart(): void {
		print("DataService started");

		// Connect player events
		this.connections.push(
			Players.PlayerAdded.Connect((player) => this.onPlayerJoined(player))
		);

		this.connections.push(
			Players.PlayerRemoving.Connect((player) => this.onPlayerLeaving(player))
		);

		// Handle players already in game
		for (const player of Players.GetPlayers()) {
			this.onPlayerJoined(player);
		}
	}

	private onPlayerJoined(player: Player): void {
		print(`Loading profile for ${player.Name}`);

		const key = `profile_${player.UserId}`;
		const [success, data] = pcall(() => {
			return this.dataStore.GetAsync(key);
		});

		let profile: PlayerProfile;

		if (success && data !== undefined && typeIs(data, "table")) {
			// Merge loaded data with default profile to ensure all fields exist
			profile = {
				...this.defaultProfile,
				...(data as Partial<PlayerProfile>)
			};
			print(`Loaded existing profile for ${player.Name}`);
		} else {
			if (!success) {
				print(`Failed to load profile for ${player.Name}: ${data}`);
			}
			profile = { ...this.defaultProfile };
			print(`Created new profile for ${player.Name}`);
		}

		this.playerProfiles.set(player, profile);
		print(`Profile updated for ${player.Name}`);
	}

	private onPlayerLeaving(player: Player): void {
		print(`Saving profile for ${player.Name}`);

		const profile = this.playerProfiles.get(player);
		if (!profile) {
			print(`No profile found for ${player.Name}`);
			return;
		}

		const key = `profile_${player.UserId}`;
		const [success, errorMessage] = pcall(() => {
			this.dataStore.SetAsync(key, profile);
		});

		if (success) {
			print(`Successfully saved profile for ${player.Name}`);
		} else {
			print(`Failed to save profile for ${player.Name}: ${errorMessage}`);
		}

		this.playerProfiles.delete(player);
	}

	public getProfile(player: Player): PlayerProfile | undefined {
		return this.playerProfiles.get(player);
	}

	public setProfile(player: Player, partial: Partial<PlayerProfile>): void {
		const existingProfile = this.playerProfiles.get(player);
		if (!existingProfile) {
			print(`No profile found for ${player.Name}`);
			return;
		}

		const updatedProfile: PlayerProfile = {
			...existingProfile,
			...partial
		};

		this.playerProfiles.set(player, updatedProfile);
		print(`Profile updated for ${player.Name}`);
		print(`Updated profile for ${player.Name}`);
	}

	public cleanup(): void {
		// Save all profiles before shutdown
		for (const [player, profile] of this.playerProfiles) {
			const key = `profile_${player.UserId}`;
			const [success, errorMessage] = pcall(() => {
				this.dataStore.SetAsync(key, profile);
			});

			if (!success) {
				print(`Failed to save profile for ${player.Name} during cleanup: ${errorMessage}`);
			}
		}

		// Disconnect all connections
		for (const connection of this.connections) {
			connection.Disconnect();
		}
		this.connections.clear();
		this.playerProfiles.clear();
	}
}