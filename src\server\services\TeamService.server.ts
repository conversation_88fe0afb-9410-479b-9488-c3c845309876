import { Service, OnStart } from "@flamework/core";
import { Players, Teams } from "@rbxts/services";
import { TeamType } from "shared/networking";

type TeamData = {
	name: TeamType;
	autoAssignable: boolean;
};

const TEAM_CONFIGS: TeamData[] = [
	{ name: "Civilians", autoAssignable: true },
	{ name: "Police", autoAssignable: false },
	{ name: "Criminals", autoAssignable: false },
	{ name: "Heroes", autoAssignable: false }
];

@Service()
export class TeamService implements OnStart {
	private teams = new Map<TeamType, Team>();

	onStart(): void {
		this.setupTeams();
		this.setupPlayerJoinHandler();
		// TODO: Setup team change handler when networking is integrated
	}

	private setupTeams(): void {
		for (const config of TEAM_CONFIGS) {
			let team = Teams.FindFirstChild(config.name) as Team | undefined;
			
			if (!team) {
				team = new Instance("Team");
				team.Name = config.name;
				team.Parent = Teams;
			}
			
			team.AutoAssignable = config.autoAssignable;
			
			this.teams.set(config.name, team);
		}
	}

	private setupPlayerJoinHandler(): void {
		Players.PlayerAdded.Connect((player) => {
			this.setTeam(player, "Civilians");
		});
	}

	// TODO: Add team change handler when GlobalEvents.requestChangeTeam is available
	// private setupTeamChangeHandler(): void { }

	private isValidTeamType(teamName: string): teamName is TeamType {
		return this.teams.has(teamName as TeamType);
	}

	getTeamByName(name: TeamType): Team | undefined {
		return this.teams.get(name);
	}

	public switchTeam(player: Player, teamName: "Police" | "Criminals" | "Heroes" | "Civilians"): boolean {
		if (!player.Parent) {
			warn(`Player ${player.Name} is no longer in game`);
			return false;
		}

		if (!this.isValidTeamType(teamName)) {
			warn(`Invalid team name: ${teamName}`);
			return false;
		}

		const team = this.getTeamByName(teamName);
		if (!team) {
			warn(`Team ${teamName} not found`);
			return false;
		}

		const currentTeam = player.Team;
		if (currentTeam && currentTeam.Name === teamName) {
			warn(`Player ${player.Name} is already on team ${teamName}`);
			return false;
		}

		player.Team = team;
		print(`Player ${player.Name} switched to team ${teamName}`);
		return true;
	}

	setTeam(player: Player, teamName: TeamType): void {
		const team = this.getTeamByName(teamName);
		
		if (!team) {
			warn(`Team ${teamName} not found`);
			return;
		}
		
		if (!player.Parent) {
			warn(`Player ${player.Name} is no longer in game`);
			return;
		}
		
		player.Team = team;
		// TODO: Send team change notification when GlobalEvents.notify is available
	}
}