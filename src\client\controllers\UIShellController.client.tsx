import { <PERSON>, OnStart } from "@flamework/core";
import React from "@rbxts/react";
import { createRoot } from "@rbxts/react-roblox";
import { Players } from "@rbxts/services";
import { App } from "client/ui/App";

@Controller()
export class UIShellController implements OnStart {
  private root?: ReturnType<typeof createRoot>;

  onStart() {
    const playerGui = Players.LocalPlayer.WaitForChild("PlayerGui") as PlayerGui;
    const screenGui = new Instance("ScreenGui");
    screenGui.Name = "CrimsonApp";
    screenGui.IgnoreGuiInset = true;
    screenGui.ResetOnSpawn = false;
    screenGui.Parent = playerGui;

    this.root = createRoot(screenGui);
    this.root.render(<App />);
  }
}