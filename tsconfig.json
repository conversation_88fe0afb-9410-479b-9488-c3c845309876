{
	"compilerOptions": {
		// Required
		"jsx": "react",
		"jsxFactory": "React.createElement",
		"jsxFragmentFactory": "React.Fragment",
		"downlevelIteration": true,
		"allowSyntheticDefaultImports": true,
		"noLib": true,
		"resolveJsonModule": true,
		"experimentalDecorators": true,
		"forceConsistentCasingInFileNames": true,
		"moduleDetection": "force",
		"module": "commonjs",
		"moduleResolution": "Node",
		"strict": true,
		"target": "ESNext",
		"typeRoots": ["node_modules/@rbxts", "node_modules/@flamework"],
		"plugins": [{ "transform": "rbxts-transformer-flamework" }],
		// Configurable
		"rootDir": "src",
		"outDir": "out",
		"baseUrl": "src",
		"incremental": true,
		"tsBuildInfoFile": "out/tsconfig.tsbuildinfo"
	}
}
